"""
واجهة تسجيل الدخول
Login Interface
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from utils.database import db_manager
from utils.helpers import UIHelpers, DataHelpers, session_manager
from config import APP_CONFIG, UI_CONFIG

class LoginWindow:
    """نافذة تسجيل الدخول"""
    
    def __init__(self):
        self.window = tk.Tk()
        self.setup_window()
        self.create_widgets()
        
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.window.title(f"{APP_CONFIG['app_name']} - تسجيل الدخول")
        self.window.configure(bg='#f0f0f0')
        
        # توسيط النافذة
        UIHelpers.center_window(self.window, 400, 300)
        
        # منع تغيير حجم النافذة
        self.window.resizable(False, False)
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.grid(row=0, column=0, sticky="nsew")
        
        # عنوان التطبيق
        title_label = ttk.Label(
            main_frame, 
            text=APP_CONFIG['app_name'],
            font=('Arial', 16, 'bold')
        )
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # حقل البريد الإلكتروني
        ttk.Label(main_frame, text="البريد الإلكتروني:").grid(
            row=1, column=0, sticky="w", pady=5
        )
        self.email_entry = ttk.Entry(main_frame, width=25)
        self.email_entry.grid(row=1, column=1, pady=5, padx=(10, 0))
        
        # حقل كلمة المرور
        ttk.Label(main_frame, text="كلمة المرور:").grid(
            row=2, column=0, sticky="w", pady=5
        )
        self.password_entry = ttk.Entry(main_frame, width=25, show="*")
        self.password_entry.grid(row=2, column=1, pady=5, padx=(10, 0))
        
        # زر تسجيل الدخول
        login_btn = ttk.Button(
            main_frame, 
            text="تسجيل الدخول", 
            command=self.login,
            width=20
        )
        login_btn.grid(row=3, column=0, columnspan=2, pady=20)
        
        # رسالة الحالة
        self.status_label = ttk.Label(main_frame, text="", foreground="red")
        self.status_label.grid(row=4, column=0, columnspan=2)
        
        # ربط مفتاح Enter بتسجيل الدخول
        self.window.bind('<Return>', lambda event: self.login())
        
        # تركيز على حقل البريد الإلكتروني
        self.email_entry.focus()
        
    def login(self):
        """تسجيل الدخول"""
        email = self.email_entry.get().strip()
        password = self.password_entry.get().strip()
        
        # التحقق من الحقول المطلوبة
        if not email or not password:
            self.show_status("يرجى إدخال البريد الإلكتروني وكلمة المرور")
            return
        
        # التحقق من صحة البريد الإلكتروني
        if not DataHelpers.validate_email(email):
            self.show_status("البريد الإلكتروني غير صحيح")
            return
        
        # تشفير كلمة المرور
        hashed_password = DataHelpers.hash_password(password)
        
        # الاتصال بقاعدة البيانات والتحقق من المستخدم
        if not db_manager.connect():
            self.show_status("خطأ في الاتصال بقاعدة البيانات")
            return
        
        try:
            user_data = db_manager.get_user_by_credentials(email, hashed_password)
            
            if user_data and len(user_data) > 0:
                user = user_data[0]
                
                # تسجيل دخول المستخدم في الجلسة
                session_manager.login(user)
                
                # إغلاق نافذة تسجيل الدخول
                self.window.destroy()
                
                # فتح لوحة التحكم الرئيسية
                self.open_dashboard()
                
            else:
                self.show_status("البريد الإلكتروني أو كلمة المرور غير صحيحة")
                
        except Exception as e:
            self.show_status(f"خطأ في تسجيل الدخول: {str(e)}")
        finally:
            db_manager.disconnect()
    
    def show_status(self, message):
        """عرض رسالة الحالة"""
        self.status_label.config(text=message)
        
    def open_dashboard(self):
        """فتح لوحة التحكم الرئيسية"""
        try:
            from modules.dashboard.main_dashboard import DashboardWindow
            dashboard = DashboardWindow()
            dashboard.run()
        except ImportError:
            messagebox.showerror("خطأ", "لم يتم العثور على وحدة لوحة التحكم")
    
    def run(self):
        """تشغيل النافذة"""
        self.window.mainloop()

def main():
    """الدالة الرئيسية"""
    app = LoginWindow()
    app.run()

if __name__ == "__main__":
    main()
