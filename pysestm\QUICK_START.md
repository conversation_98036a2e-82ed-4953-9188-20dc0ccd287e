# دليل البدء السريع - نظام ERP سطح المكتب

## خطوات التشغيل السريع

### 1. إعداد قاعدة البيانات
```sql
-- في MySQL Command Line أو phpMyAdmin
source database_setup.sql
```

### 2. تحديث إعدادات قاعدة البيانات
قم بتعديل ملف `config.py`:
```python
DATABASE_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'database': 'erp_desktop',
    'user': 'root',           # اسم المستخدم
    'password': '',           # كلمة المرور
    'charset': 'utf8mb4'
}
```

### 3. تشغيل البرنامج

#### على Windows:
```cmd
run.bat
```

#### على Linux/macOS:
```bash
chmod +x run.sh
./run.sh
```

#### تشغيل مباشر:
```bash
python main.py
```

## بيانات تسجيل الدخول الافتراضية

- **البريد الإلكتروني:** <EMAIL>
- **كلمة المرور:** 123456

## الوحدات المتاحة

### ✅ مكتملة
- تسجيل الدخول
- لوحة التحكم الرئيسية
- إدارة المستخدمين
- نقاط البيع (POS)

### 🚧 قيد التطوير
- إدارة المنتجات والمخزون
- الفواتير والمبيعات
- التقارير
- الإعدادات

## استكشاف الأخطاء

### خطأ في الاتصال بقاعدة البيانات
1. تأكد من تشغيل MySQL Server
2. تحقق من إعدادات الاتصال في `config.py`
3. تأكد من إنشاء قاعدة البيانات

### خطأ في المكتبات
```bash
pip install --upgrade pip
pip install -r requirements.txt
```

### خطأ في الترميز
- تأكد من حفظ الملفات بترميز UTF-8
- تأكد من إعداد قاعدة البيانات بترميز utf8mb4

## الميزات الرئيسية

### نقاط البيع
- إضافة المنتجات للسلة
- حساب الإجماليات تلقائياً
- دعم طرق دفع متعددة
- تحديث المخزون تلقائياً

### إدارة المستخدمين
- إضافة وتعديل المستخدمين
- إدارة الصلاحيات
- البحث والتصفية

### لوحة التحكم
- إحصائيات سريعة
- الأنشطة الحديثة
- وصول سريع للوحدات

## التطوير المستقبلي

المرحلة التالية ستشمل:
- إكمال وحدة إدارة المنتجات
- تطوير نظام الفواتير
- إضافة التقارير المتقدمة
- تحسين واجهة المستخدم

## الدعم

للحصول على المساعدة:
1. راجع ملف README.md للتفاصيل الكاملة
2. تحقق من ملفات السجلات في مجلد logs/
3. تواصل مع فريق التطوير
