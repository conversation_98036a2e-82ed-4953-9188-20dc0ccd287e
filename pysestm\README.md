# نظام ERP سطح المكتب
## Desktop ERP System

نظام إدارة موارد المؤسسة (ERP) مطور بلغة Python باستخدام مكتبة Tkinter لواجهة المستخدم.

## المتطلبات

### متطلبات النظام
- Python 3.8 أو أحدث
- MySQL Server 5.7 أو أحدث
- نظام التشغيل: Windows, Linux, macOS

### المكتبات المطلوبة
```bash
pip install -r requirements.txt
```

## التثبيت والإعداد

### 1. تحضير قاعدة البيانات
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE erp_desktop CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- <PERSON><PERSON><PERSON><PERSON><PERSON> مستخدم (اختياري)
CREATE USER 'erp_user'@'localhost' IDENTIFIED BY 'password';
GRANT ALL PRIVILEGES ON erp_desktop.* TO 'erp_user'@'localhost';
FLUSH PRIVILEGES;
```

### 2. إعداد الاتصال بقاعدة البيانات
قم بتعديل ملف `config.py` وتحديث إعدادات قاعدة البيانات:

```python
DATABASE_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'database': 'erp_desktop',
    'user': 'your_username',
    'password': 'your_password',
    'charset': 'utf8mb4'
}
```

### 3. تشغيل البرنامج
```bash
cd pysestm
python main.py
```

## الوحدات المتوفرة

### 1. وحدة المصادقة (Authentication)
- تسجيل الدخول
- إدارة الجلسات
- الأمان

### 2. لوحة التحكم الرئيسية (Dashboard)
- عرض الإحصائيات السريعة
- الأنشطة الحديثة
- الوصول السريع للوحدات

### 3. إدارة المستخدمين (User Management)
- إضافة وتعديل المستخدمين
- إدارة الصلاحيات
- البحث والتصفية

### 4. نقاط البيع (POS System)
- إدارة المبيعات
- حساب الإجماليات
- طرق الدفع المختلفة
- إدارة المخزون

### 5. إدارة المنتجات والمخزون (قيد التطوير)
- إضافة وتعديل المنتجات
- تتبع المخزون
- تقارير المخزون

### 6. الفواتير والمبيعات (قيد التطوير)
- إنشاء الفواتير
- إدارة العملاء
- تتبع المدفوعات

### 7. التقارير (قيد التطوير)
- تقارير المبيعات
- التقارير المالية
- تقارير المخزون

### 8. الإعدادات (قيد التطوير)
- إعدادات الشركة
- إعدادات النظام
- النسخ الاحتياطي

## هيكل المشروع

```
pysestm/
├── main.py                 # الملف الرئيسي
├── config.py              # إعدادات التطبيق
├── requirements.txt       # المكتبات المطلوبة
├── README.md             # دليل الاستخدام
├── utils/                # الأدوات المساعدة
│   ├── __init__.py
│   ├── database.py       # إدارة قاعدة البيانات
│   └── helpers.py        # الدوال المساعدة
├── modules/              # وحدات التطبيق
│   ├── auth/            # وحدة المصادقة
│   ├── dashboard/       # لوحة التحكم
│   ├── users/           # إدارة المستخدمين
│   ├── pos/             # نقاط البيع
│   ├── inventory/       # إدارة المخزون
│   ├── invoices/        # الفواتير
│   ├── reports/         # التقارير
│   └── settings/        # الإعدادات
├── data/                # ملفات البيانات
├── assets/              # الموارد (صور، أيقونات)
└── logs/                # ملفات السجلات
```

## الاستخدام

### تسجيل الدخول
1. قم بتشغيل البرنامج
2. أدخل البريد الإلكتروني وكلمة المرور
3. اضغط "تسجيل الدخول"

### استخدام نقاط البيع
1. من لوحة التحكم، اختر "المبيعات" > "نقاط البيع"
2. ابحث عن المنتج المطلوب
3. انقر نقراً مزدوجاً لإضافته للسلة
4. أدخل طريقة الدفع والمبلغ المدفوع
5. اضغط "إتمام البيع"

### إدارة المستخدمين
1. من لوحة التحكم، اختر "الإعدادات" > "إدارة المستخدمين"
2. اضغط "إضافة مستخدم" لإضافة مستخدم جديد
3. املأ البيانات المطلوبة واضغط "حفظ"

## المشاكل الشائعة

### خطأ في الاتصال بقاعدة البيانات
- تأكد من تشغيل MySQL Server
- تحقق من إعدادات الاتصال في `config.py`
- تأكد من وجود قاعدة البيانات

### خطأ في استيراد المكتبات
```bash
pip install --upgrade pip
pip install -r requirements.txt
```

### مشاكل في الترميز
- تأكد من أن قاعدة البيانات تستخدم ترميز UTF-8
- تأكد من حفظ الملفات بترميز UTF-8

## التطوير المستقبلي

- [ ] إكمال وحدة إدارة المنتجات
- [ ] إضافة وحدة الفواتير
- [ ] تطوير نظام التقارير
- [ ] إضافة وحدة الموارد البشرية
- [ ] تحسين واجهة المستخدم
- [ ] إضافة النسخ الاحتياطي التلقائي
- [ ] دعم قواعد بيانات أخرى

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.
