"""
نظام نقاط البيع
Point of Sale System
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os
from datetime import datetime

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from utils.database import db_manager
from utils.helpers import UIHelpers, DataHelpers, session_manager
from config import APP_CONFIG

class POSWindow:
    """نافذة نقاط البيع"""
    
    def __init__(self):
        self.window = tk.Toplevel()
        self.cart_items = []
        self.total_amount = 0.0
        self.setup_window()
        self.create_widgets()
        self.load_products()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("نقاط البيع - POS")
        self.window.configure(bg='#f0f0f0')
        UIHelpers.center_window(self.window, 1200, 700)
        
        # إعداد الشبكة
        self.window.grid_rowconfigure(0, weight=1)
        self.window.grid_columnconfigure(0, weight=1)
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.grid(row=0, column=0, sticky="nsew")
        main_frame.grid_rowconfigure(0, weight=1)
        main_frame.grid_columnconfigure(0, weight=2)
        main_frame.grid_columnconfigure(1, weight=1)
        
        # قسم المنتجات (يسار)
        self.create_products_section(main_frame)
        
        # قسم السلة والدفع (يمين)
        self.create_cart_section(main_frame)
        
    def create_products_section(self, parent):
        """إنشاء قسم المنتجات"""
        products_frame = ttk.LabelFrame(parent, text="المنتجات", padding="10")
        products_frame.grid(row=0, column=0, sticky="nsew", padx=(0, 5))
        products_frame.grid_rowconfigure(1, weight=1)
        products_frame.grid_columnconfigure(0, weight=1)
        
        # شريط البحث
        search_frame = ttk.Frame(products_frame)
        search_frame.grid(row=0, column=0, sticky="ew", pady=(0, 10))
        search_frame.grid_columnconfigure(1, weight=1)
        
        ttk.Label(search_frame, text="البحث:").grid(row=0, column=0, sticky="w")
        self.search_entry = ttk.Entry(search_frame)
        self.search_entry.grid(row=0, column=1, sticky="ew", padx=(5, 0))
        self.search_entry.bind('<KeyRelease>', self.search_products)
        
        # جدول المنتجات
        self.create_products_table(products_frame)
        
    def create_products_table(self, parent):
        """إنشاء جدول المنتجات"""
        # إطار الجدول
        table_frame = ttk.Frame(parent)
        table_frame.grid(row=1, column=0, sticky="nsew")
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # الجدول
        columns = ('id', 'name', 'sku', 'price', 'quantity')
        self.products_tree = ttk.Treeview(table_frame, columns=columns, show='headings')
        
        # تعيين العناوين
        headings = ['المعرف', 'اسم المنتج', 'الرمز', 'السعر', 'الكمية']
        for col, heading in zip(columns, headings):
            self.products_tree.heading(col, text=heading)
            self.products_tree.column(col, width=100)
        
        # إخفاء عمود المعرف
        self.products_tree.column('id', width=0, stretch=False)
        self.products_tree.heading('id', text='')
        
        self.products_tree.grid(row=0, column=0, sticky="nsew")
        
        # شريط التمرير
        scrollbar_y = ttk.Scrollbar(table_frame, orient="vertical", command=self.products_tree.yview)
        scrollbar_y.grid(row=0, column=1, sticky="ns")
        self.products_tree.configure(yscrollcommand=scrollbar_y.set)
        
        # ربط النقر المزدوج لإضافة المنتج للسلة
        self.products_tree.bind('<Double-1>', self.add_to_cart)
        
    def create_cart_section(self, parent):
        """إنشاء قسم السلة والدفع"""
        cart_frame = ttk.LabelFrame(parent, text="السلة والدفع", padding="10")
        cart_frame.grid(row=0, column=1, sticky="nsew", padx=(5, 0))
        cart_frame.grid_rowconfigure(1, weight=1)
        cart_frame.grid_columnconfigure(0, weight=1)
        
        # أزرار التحكم
        control_frame = ttk.Frame(cart_frame)
        control_frame.grid(row=0, column=0, sticky="ew", pady=(0, 10))
        
        ttk.Button(control_frame, text="إضافة للسلة", command=self.add_to_cart).pack(side="left", padx=(0, 5))
        ttk.Button(control_frame, text="حذف من السلة", command=self.remove_from_cart).pack(side="left", padx=(0, 5))
        ttk.Button(control_frame, text="مسح السلة", command=self.clear_cart).pack(side="left")
        
        # جدول السلة
        self.create_cart_table(cart_frame)
        
        # معلومات الإجمالي
        self.create_total_section(cart_frame)
        
        # أزرار الدفع
        self.create_payment_section(cart_frame)
        
    def create_cart_table(self, parent):
        """إنشاء جدول السلة"""
        # إطار الجدول
        table_frame = ttk.Frame(parent)
        table_frame.grid(row=1, column=0, sticky="nsew", pady=(0, 10))
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # الجدول
        columns = ('name', 'price', 'quantity', 'total')
        self.cart_tree = ttk.Treeview(table_frame, columns=columns, show='headings')
        
        # تعيين العناوين
        headings = ['المنتج', 'السعر', 'الكمية', 'الإجمالي']
        for col, heading in zip(columns, headings):
            self.cart_tree.heading(col, text=heading)
            self.cart_tree.column(col, width=80)
        
        self.cart_tree.grid(row=0, column=0, sticky="nsew")
        
        # شريط التمرير
        scrollbar_cart = ttk.Scrollbar(table_frame, orient="vertical", command=self.cart_tree.yview)
        scrollbar_cart.grid(row=0, column=1, sticky="ns")
        self.cart_tree.configure(yscrollcommand=scrollbar_cart.set)
        
    def create_total_section(self, parent):
        """إنشاء قسم الإجمالي"""
        total_frame = ttk.LabelFrame(parent, text="الإجمالي", padding="10")
        total_frame.grid(row=2, column=0, sticky="ew", pady=(0, 10))
        
        # إجمالي المبلغ
        self.total_label = ttk.Label(total_frame, text="0.00 ريال", font=('Arial', 16, 'bold'))
        self.total_label.pack()
        
    def create_payment_section(self, parent):
        """إنشاء قسم الدفع"""
        payment_frame = ttk.LabelFrame(parent, text="الدفع", padding="10")
        payment_frame.grid(row=3, column=0, sticky="ew")
        
        # طريقة الدفع
        ttk.Label(payment_frame, text="طريقة الدفع:").grid(row=0, column=0, sticky="w", pady=5)
        self.payment_method = ttk.Combobox(payment_frame, values=['نقدي', 'بطاقة ائتمان', 'تحويل بنكي'])
        self.payment_method.grid(row=0, column=1, sticky="ew", padx=(5, 0), pady=5)
        self.payment_method.set('نقدي')
        
        # المبلغ المدفوع
        ttk.Label(payment_frame, text="المبلغ المدفوع:").grid(row=1, column=0, sticky="w", pady=5)
        self.paid_amount_entry = ttk.Entry(payment_frame)
        self.paid_amount_entry.grid(row=1, column=1, sticky="ew", padx=(5, 0), pady=5)
        
        # الباقي
        ttk.Label(payment_frame, text="الباقي:").grid(row=2, column=0, sticky="w", pady=5)
        self.change_label = ttk.Label(payment_frame, text="0.00 ريال")
        self.change_label.grid(row=2, column=1, sticky="w", padx=(5, 0), pady=5)
        
        # ربط حساب الباقي
        self.paid_amount_entry.bind('<KeyRelease>', self.calculate_change)
        
        # أزرار الدفع
        buttons_frame = ttk.Frame(payment_frame)
        buttons_frame.grid(row=3, column=0, columnspan=2, pady=10)
        
        ttk.Button(buttons_frame, text="إتمام البيع", command=self.complete_sale).pack(side="left", padx=(0, 5))
        ttk.Button(buttons_frame, text="طباعة الفاتورة", command=self.print_receipt).pack(side="left")
        
        payment_frame.grid_columnconfigure(1, weight=1)
        
    def load_products(self):
        """تحميل المنتجات"""
        # مسح البيانات السابقة
        for item in self.products_tree.get_children():
            self.products_tree.delete(item)
        
        if not db_manager.connect():
            messagebox.showerror("خطأ", "خطأ في الاتصال بقاعدة البيانات")
            return
        
        try:
            # استعلام المنتجات
            query = """
            SELECT id, name, sku, sale_price, quantity 
            FROM product_services 
            WHERE created_by = %s AND is_active = 1 AND quantity > 0
            ORDER BY name
            """
            products = db_manager.execute_query(query, (session_manager.get_user_id(),))
            
            if products:
                for product in products:
                    self.products_tree.insert('', 'end', values=(
                        product['id'],
                        product['name'],
                        product['sku'] or '',
                        f"{product['sale_price']:.2f}",
                        product['quantity']
                    ))
                    
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل المنتجات: {str(e)}")
        finally:
            db_manager.disconnect()
    
    def search_products(self, event=None):
        """البحث في المنتجات"""
        search_term = self.search_entry.get().lower()
        
        # إخفاء جميع العناصر
        for item in self.products_tree.get_children():
            self.products_tree.delete(item)
        
        if not db_manager.connect():
            return
        
        try:
            # استعلام البحث
            query = """
            SELECT id, name, sku, sale_price, quantity 
            FROM product_services 
            WHERE created_by = %s AND is_active = 1 AND quantity > 0
            AND (LOWER(name) LIKE %s OR LOWER(sku) LIKE %s)
            ORDER BY name
            """
            search_pattern = f"%{search_term}%"
            products = db_manager.execute_query(query, (session_manager.get_user_id(), search_pattern, search_pattern))
            
            if products:
                for product in products:
                    self.products_tree.insert('', 'end', values=(
                        product['id'],
                        product['name'],
                        product['sku'] or '',
                        f"{product['sale_price']:.2f}",
                        product['quantity']
                    ))
                    
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في البحث: {str(e)}")
        finally:
            db_manager.disconnect()
    
    def add_to_cart(self, event=None):
        """إضافة منتج للسلة"""
        selected = self.products_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج لإضافته للسلة")
            return
        
        product_data = self.products_tree.item(selected[0])['values']
        product_id = product_data[0]
        product_name = product_data[1]
        product_price = float(product_data[3])
        available_quantity = int(product_data[4])
        
        # التحقق من وجود المنتج في السلة
        existing_item = None
        for item in self.cart_items:
            if item['id'] == product_id:
                existing_item = item
                break
        
        if existing_item:
            # زيادة الكمية
            if existing_item['quantity'] < available_quantity:
                existing_item['quantity'] += 1
                existing_item['total'] = existing_item['quantity'] * existing_item['price']
            else:
                messagebox.showwarning("تحذير", "الكمية المطلوبة غير متوفرة في المخزون")
                return
        else:
            # إضافة منتج جديد
            cart_item = {
                'id': product_id,
                'name': product_name,
                'price': product_price,
                'quantity': 1,
                'total': product_price
            }
            self.cart_items.append(cart_item)
        
        self.update_cart_display()
        self.calculate_total()
    
    def remove_from_cart(self):
        """حذف منتج من السلة"""
        selected = self.cart_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج لحذفه من السلة")
            return
        
        item_index = self.cart_tree.index(selected[0])
        del self.cart_items[item_index]
        
        self.update_cart_display()
        self.calculate_total()
    
    def clear_cart(self):
        """مسح السلة"""
        if self.cart_items and messagebox.askyesno("تأكيد", "هل تريد مسح جميع المنتجات من السلة؟"):
            self.cart_items.clear()
            self.update_cart_display()
            self.calculate_total()
    
    def update_cart_display(self):
        """تحديث عرض السلة"""
        # مسح العرض السابق
        for item in self.cart_tree.get_children():
            self.cart_tree.delete(item)
        
        # إضافة المنتجات
        for item in self.cart_items:
            self.cart_tree.insert('', 'end', values=(
                item['name'],
                f"{item['price']:.2f}",
                item['quantity'],
                f"{item['total']:.2f}"
            ))
    
    def calculate_total(self):
        """حساب الإجمالي"""
        self.total_amount = sum(item['total'] for item in self.cart_items)
        self.total_label.config(text=f"{self.total_amount:.2f} ريال")
        self.calculate_change()
    
    def calculate_change(self, event=None):
        """حساب الباقي"""
        try:
            paid_amount = float(self.paid_amount_entry.get() or 0)
            change = paid_amount - self.total_amount
            self.change_label.config(text=f"{change:.2f} ريال")
        except ValueError:
            self.change_label.config(text="0.00 ريال")
    
    def complete_sale(self):
        """إتمام البيع"""
        if not self.cart_items:
            messagebox.showwarning("تحذير", "السلة فارغة")
            return
        
        try:
            paid_amount = float(self.paid_amount_entry.get() or 0)
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح")
            return
        
        if paid_amount < self.total_amount:
            messagebox.showerror("خطأ", "المبلغ المدفوع أقل من إجمالي الفاتورة")
            return
        
        if not db_manager.connect():
            messagebox.showerror("خطأ", "خطأ في الاتصال بقاعدة البيانات")
            return
        
        try:
            # إنشاء فاتورة POS
            pos_query = """
            INSERT INTO pos (pos_id, customer_id, warehouse_id, total_amount, payment_method, 
                           paid_amount, change_amount, created_by, created_at) 
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            pos_id = f"POS-{datetime.now().strftime('%Y%m%d%H%M%S')}"
            change_amount = paid_amount - self.total_amount
            
            pos_params = (
                pos_id, 1, 1, self.total_amount, self.payment_method.get(),
                paid_amount, change_amount, session_manager.get_user_id(), datetime.now()
            )
            
            result = db_manager.execute_query(pos_query, pos_params)
            
            if result:
                # إضافة تفاصيل المنتجات
                for item in self.cart_items:
                    detail_query = """
                    INSERT INTO pos_products (pos_id, product_id, quantity, price, total) 
                    VALUES (%s, %s, %s, %s, %s)
                    """
                    detail_params = (pos_id, item['id'], item['quantity'], item['price'], item['total'])
                    db_manager.execute_query(detail_query, detail_params)
                    
                    # تحديث المخزون
                    update_stock_query = """
                    UPDATE product_services 
                    SET quantity = quantity - %s 
                    WHERE id = %s
                    """
                    db_manager.execute_query(update_stock_query, (item['quantity'], item['id']))
                
                messagebox.showinfo("نجح", f"تم إتمام البيع بنجاح\nرقم الفاتورة: {pos_id}")
                
                # مسح السلة
                self.cart_items.clear()
                self.update_cart_display()
                self.calculate_total()
                self.paid_amount_entry.delete(0, tk.END)
                
                # تحديث المنتجات
                self.load_products()
                
            else:
                messagebox.showerror("خطأ", "فشل في إتمام البيع")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إتمام البيع: {str(e)}")
        finally:
            db_manager.disconnect()
    
    def print_receipt(self):
        """طباعة الفاتورة"""
        if not self.cart_items:
            messagebox.showwarning("تحذير", "لا توجد منتجات للطباعة")
            return
        
        # هنا يمكن إضافة كود الطباعة
        messagebox.showinfo("طباعة", "سيتم إضافة وظيفة الطباعة قريباً")
    
    def run(self):
        """تشغيل النافذة"""
        self.window.mainloop()

def main():
    """الدالة الرئيسية"""
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    app = POSWindow()
    app.run()

if __name__ == "__main__":
    main()
