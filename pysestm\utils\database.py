"""
إدارة قاعدة البيانات
Database Management
"""

import mysql.connector
from mysql.connector import Error
from config import DATABASE_CONFIG
import logging

class DatabaseManager:
    """مدير قاعدة البيانات"""
    
    def __init__(self):
        self.connection = None
        self.cursor = None
        
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.connection = mysql.connector.connect(**DATABASE_CONFIG)
            self.cursor = self.connection.cursor(dictionary=True)
            return True
        except Error as e:
            logging.error(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            return False
    
    def disconnect(self):
        """قطع الاتصال بقاعدة البيانات"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
    
    def execute_query(self, query, params=None):
        """تنفيذ استعلام"""
        try:
            if not self.connection or not self.connection.is_connected():
                self.connect()
            
            self.cursor.execute(query, params or ())
            
            if query.strip().upper().startswith('SELECT'):
                return self.cursor.fetchall()
            else:
                self.connection.commit()
                return self.cursor.rowcount
                
        except Error as e:
            logging.error(f"خطأ في تنفيذ الاستعلام: {e}")
            if self.connection:
                self.connection.rollback()
            return None
    
    def execute_many(self, query, data_list):
        """تنفيذ استعلامات متعددة"""
        try:
            if not self.connection or not self.connection.is_connected():
                self.connect()
            
            self.cursor.executemany(query, data_list)
            self.connection.commit()
            return self.cursor.rowcount
            
        except Error as e:
            logging.error(f"خطأ في تنفيذ الاستعلامات المتعددة: {e}")
            if self.connection:
                self.connection.rollback()
            return None
    
    def get_user_by_credentials(self, email, password):
        """التحقق من بيانات المستخدم"""
        query = """
        SELECT id, name, email, type, created_by, is_active 
        FROM users 
        WHERE email = %s AND password = %s AND is_active = 1
        """
        return self.execute_query(query, (email, password))
    
    def get_dashboard_stats(self, user_id):
        """الحصول على إحصائيات لوحة التحكم"""
        stats = {}
        
        # إجمالي المبيعات
        query = "SELECT COUNT(*) as total_sales FROM pos WHERE created_by = %s"
        result = self.execute_query(query, (user_id,))
        stats['total_sales'] = result[0]['total_sales'] if result else 0
        
        # إجمالي المنتجات
        query = "SELECT COUNT(*) as total_products FROM product_services WHERE created_by = %s"
        result = self.execute_query(query, (user_id,))
        stats['total_products'] = result[0]['total_products'] if result else 0
        
        # إجمالي العملاء
        query = "SELECT COUNT(*) as total_customers FROM customers WHERE created_by = %s"
        result = self.execute_query(query, (user_id,))
        stats['total_customers'] = result[0]['total_customers'] if result else 0
        
        return stats

# إنشاء مثيل عام من مدير قاعدة البيانات
db_manager = DatabaseManager()
