"""
الدوال المساعدة
Helper Functions
"""

import tkinter as tk
from tkinter import messagebox, ttk
from datetime import datetime
import hashlib
import re

class UIHelpers:
    """مساعدات واجهة المستخدم"""
    
    @staticmethod
    def center_window(window, width=800, height=600):
        """توسيط النافذة على الشاشة"""
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()
        
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        
        window.geometry(f"{width}x{height}+{x}+{y}")
    
    @staticmethod
    def create_labeled_entry(parent, label_text, row, column=0, columnspan=2, sticky="ew"):
        """إنشاء حقل إدخال مع تسمية"""
        label = ttk.Label(parent, text=label_text)
        label.grid(row=row, column=column, sticky="w", padx=5, pady=2)
        
        entry = ttk.Entry(parent)
        entry.grid(row=row, column=column+1, sticky=sticky, padx=5, pady=2)
        
        return entry
    
    @staticmethod
    def create_button_frame(parent, buttons_config):
        """إنشاء إطار للأزرار"""
        frame = ttk.Frame(parent)
        
        for i, (text, command) in enumerate(buttons_config):
            btn = ttk.Button(frame, text=text, command=command)
            btn.grid(row=0, column=i, padx=5, pady=5)
        
        return frame
    
    @staticmethod
    def show_message(title, message, msg_type="info"):
        """عرض رسالة للمستخدم"""
        if msg_type == "info":
            messagebox.showinfo(title, message)
        elif msg_type == "warning":
            messagebox.showwarning(title, message)
        elif msg_type == "error":
            messagebox.showerror(title, message)
        elif msg_type == "question":
            return messagebox.askyesno(title, message)
    
    @staticmethod
    def create_treeview(parent, columns, headings):
        """إنشاء جدول بيانات"""
        tree = ttk.Treeview(parent, columns=columns, show='headings')
        
        for col, heading in zip(columns, headings):
            tree.heading(col, text=heading)
            tree.column(col, width=100)
        
        # إضافة شريط التمرير
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)
        
        return tree, scrollbar

class DataHelpers:
    """مساعدات البيانات"""
    
    @staticmethod
    def hash_password(password):
        """تشفير كلمة المرور"""
        return hashlib.md5(password.encode()).hexdigest()
    
    @staticmethod
    def validate_email(email):
        """التحقق من صحة البريد الإلكتروني"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    @staticmethod
    def format_currency(amount):
        """تنسيق المبلغ المالي"""
        return f"{amount:,.2f} ريال"
    
    @staticmethod
    def format_date(date_obj):
        """تنسيق التاريخ"""
        if isinstance(date_obj, str):
            return date_obj
        return date_obj.strftime("%Y-%m-%d") if date_obj else ""
    
    @staticmethod
    def format_datetime(datetime_obj):
        """تنسيق التاريخ والوقت"""
        if isinstance(datetime_obj, str):
            return datetime_obj
        return datetime_obj.strftime("%Y-%m-%d %H:%M:%S") if datetime_obj else ""
    
    @staticmethod
    def validate_required_fields(fields_dict):
        """التحقق من الحقول المطلوبة"""
        empty_fields = []
        for field_name, field_value in fields_dict.items():
            if not field_value or str(field_value).strip() == "":
                empty_fields.append(field_name)
        return empty_fields

class SessionManager:
    """مدير الجلسة"""
    
    def __init__(self):
        self.current_user = None
        self.login_time = None
    
    def login(self, user_data):
        """تسجيل دخول المستخدم"""
        self.current_user = user_data
        self.login_time = datetime.now()
    
    def logout(self):
        """تسجيل خروج المستخدم"""
        self.current_user = None
        self.login_time = None
    
    def is_logged_in(self):
        """التحقق من تسجيل الدخول"""
        return self.current_user is not None
    
    def get_user_id(self):
        """الحصول على معرف المستخدم"""
        return self.current_user['id'] if self.current_user else None
    
    def get_user_name(self):
        """الحصول على اسم المستخدم"""
        return self.current_user['name'] if self.current_user else ""
    
    def get_user_type(self):
        """الحصول على نوع المستخدم"""
        return self.current_user['type'] if self.current_user else ""

# إنشاء مثيل عام من مدير الجلسة
session_manager = SessionManager()
