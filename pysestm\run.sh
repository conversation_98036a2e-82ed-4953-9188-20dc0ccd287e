#!/bin/bash

echo "========================================"
echo "    نظام ERP سطح المكتب"
echo "    Desktop ERP System"
echo "========================================"
echo

# التحقق من Python
echo "تحقق من Python..."
if ! command -v python3 &> /dev/null; then
    echo "خطأ: Python3 غير مثبت"
    echo "يرجى تثبيت Python 3.8 أو أحدث"
    exit 1
fi

# التحقق من pip
if ! command -v pip3 &> /dev/null; then
    echo "خطأ: pip3 غير مثبت"
    exit 1
fi

# التحقق من المكتبات المطلوبة
echo "تحقق من المكتبات المطلوبة..."
if ! pip3 show mysql-connector-python &> /dev/null; then
    echo "تثبيت المكتبات المطلوبة..."
    pip3 install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "خطأ في تثبيت المكتبات"
        exit 1
    fi
fi

# بدء تشغيل البرنامج
echo "بدء تشغيل البرنامج..."
python3 main.py

if [ $? -ne 0 ]; then
    echo
    echo "حدث خطأ في تشغيل البرنامج"
    echo "يرجى التحقق من:"
    echo "1. إعدادات قاعدة البيانات في config.py"
    echo "2. تشغيل MySQL Server"
    echo "3. إنشاء قاعدة البيانات باستخدام database_setup.sql"
    echo
fi

read -p "اضغط Enter للمتابعة..."
