"""
الملف الرئيسي لتطبيق ERP سطح المكتب
Main Application File for Desktop ERP System
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os
import logging
from datetime import datetime

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(__file__))

# استيراد الوحدات
from config import APP_CONFIG, create_directories
from utils.database import db_manager
from utils.helpers import session_manager
from modules.auth.login import LoginWindow

class ERPApplication:
    """التطبيق الرئيسي لنظام ERP"""
    
    def __init__(self):
        self.setup_logging()
        self.setup_directories()
        self.check_database_connection()
        
    def setup_logging(self):
        """إعداد نظام السجلات"""
        log_dir = os.path.join(os.path.dirname(__file__), 'logs')
        os.makedirs(log_dir, exist_ok=True)
        
        log_file = os.path.join(log_dir, f"erp_{datetime.now().strftime('%Y%m%d')}.log")
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        logging.info("تم بدء تشغيل التطبيق")
    
    def setup_directories(self):
        """إعداد المجلدات المطلوبة"""
        try:
            create_directories()
            logging.info("تم إنشاء المجلدات المطلوبة")
        except Exception as e:
            logging.error(f"خطأ في إنشاء المجلدات: {e}")
    
    def check_database_connection(self):
        """التحقق من الاتصال بقاعدة البيانات"""
        try:
            if db_manager.connect():
                logging.info("تم الاتصال بقاعدة البيانات بنجاح")
                db_manager.disconnect()
                return True
            else:
                logging.error("فشل في الاتصال بقاعدة البيانات")
                return False
        except Exception as e:
            logging.error(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            return False
    
    def show_startup_error(self, message):
        """عرض رسالة خطأ عند بدء التشغيل"""
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("خطأ في بدء التشغيل", message)
        root.destroy()
    
    def run(self):
        """تشغيل التطبيق"""
        try:
            # التحقق من الاتصال بقاعدة البيانات
            if not self.check_database_connection():
                self.show_startup_error(
                    "لا يمكن الاتصال بقاعدة البيانات.\n"
                    "يرجى التأكد من إعدادات قاعدة البيانات في ملف config.py"
                )
                return
            
            # بدء تشغيل واجهة تسجيل الدخول
            logging.info("بدء تشغيل واجهة تسجيل الدخول")
            login_window = LoginWindow()
            login_window.run()
            
        except Exception as e:
            logging.error(f"خطأ في تشغيل التطبيق: {e}")
            self.show_startup_error(f"خطأ في تشغيل التطبيق:\n{str(e)}")

def main():
    """الدالة الرئيسية"""
    try:
        # إنشاء وتشغيل التطبيق
        app = ERPApplication()
        app.run()
        
    except KeyboardInterrupt:
        logging.info("تم إيقاف التطبيق بواسطة المستخدم")
    except Exception as e:
        logging.error(f"خطأ غير متوقع: {e}")
        # عرض رسالة خطأ للمستخدم
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("خطأ غير متوقع", f"حدث خطأ غير متوقع:\n{str(e)}")
        root.destroy()
    finally:
        logging.info("تم إنهاء التطبيق")

if __name__ == "__main__":
    main()
