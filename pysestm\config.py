"""
إعدادات التطبيق
Application Configuration
"""

import os
from pathlib import Path

# مسارات التطبيق
BASE_DIR = Path(__file__).parent
DATA_DIR = BASE_DIR / "data"
ASSETS_DIR = BASE_DIR / "assets"

# إعدادات قاعدة البيانات
DATABASE_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'database': 'erp_desktop',
    'user': 'root',
    'password': '',
    'charset': 'utf8mb4'
}

# إعدادات الواجهة
UI_CONFIG = {
    'theme': 'default',
    'language': 'ar',
    'font_family': 'Arial',
    'font_size': 12,
    'window_width': 1200,
    'window_height': 800
}

# إعدادات الأمان
SECURITY_CONFIG = {
    'session_timeout': 3600,  # ثانية
    'max_login_attempts': 3,
    'password_min_length': 6
}

# إعدادات التطبيق
APP_CONFIG = {
    'app_name': 'نظام ERP سطح المكتب',
    'version': '1.0.0',
    'debug': True
}

# إنشاء المجلدات المطلوبة
def create_directories():
    """إنشاء المجلدات المطلوبة للتطبيق"""
    directories = [DATA_DIR, ASSETS_DIR]
    for directory in directories:
        directory.mkdir(exist_ok=True)

if __name__ == "__main__":
    create_directories()
