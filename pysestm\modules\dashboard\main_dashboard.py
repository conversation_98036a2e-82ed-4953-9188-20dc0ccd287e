"""
لوحة التحكم الرئيسية
Main Dashboard
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from utils.database import db_manager
from utils.helpers import UIHelpers, session_manager
from config import APP_CONFIG, UI_CONFIG

class DashboardWindow:
    """نافذة لوحة التحكم الرئيسية"""
    
    def __init__(self):
        self.window = tk.Tk()
        self.setup_window()
        self.create_menu()
        self.create_widgets()
        self.load_dashboard_data()
        
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.window.title(f"{APP_CONFIG['app_name']} - لوحة التحكم")
        self.window.configure(bg='#f0f0f0')
        
        # توسيط النافذة
        UIHelpers.center_window(
            self.window, 
            UI_CONFIG['window_width'], 
            UI_CONFIG['window_height']
        )
        
        # إعداد الشبكة
        self.window.grid_rowconfigure(0, weight=1)
        self.window.grid_columnconfigure(0, weight=1)
        
    def create_menu(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self.window)
        self.window.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="تسجيل خروج", command=self.logout)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.window.quit)
        
        # قائمة المبيعات
        sales_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="المبيعات", menu=sales_menu)
        sales_menu.add_command(label="نقاط البيع", command=self.open_pos)
        sales_menu.add_command(label="الفواتير", command=self.open_invoices)
        sales_menu.add_command(label="العملاء", command=self.open_customers)
        
        # قائمة المخزون
        inventory_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="المخزون", menu=inventory_menu)
        inventory_menu.add_command(label="المنتجات", command=self.open_products)
        inventory_menu.add_command(label="المخازن", command=self.open_warehouses)
        inventory_menu.add_command(label="تقارير المخزون", command=self.open_inventory_reports)
        
        # قائمة الموارد البشرية
        hr_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="الموارد البشرية", menu=hr_menu)
        hr_menu.add_command(label="الموظفين", command=self.open_employees)
        hr_menu.add_command(label="الحضور والانصراف", command=self.open_attendance)
        hr_menu.add_command(label="الرواتب", command=self.open_payroll)
        
        # قائمة التقارير
        reports_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="التقارير", menu=reports_menu)
        reports_menu.add_command(label="تقارير المبيعات", command=self.open_sales_reports)
        reports_menu.add_command(label="التقارير المالية", command=self.open_financial_reports)
        reports_menu.add_command(label="تقارير المخزون", command=self.open_inventory_reports)
        
        # قائمة الإعدادات
        settings_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="الإعدادات", menu=settings_menu)
        settings_menu.add_command(label="إعدادات الشركة", command=self.open_company_settings)
        settings_menu.add_command(label="إدارة المستخدمين", command=self.open_users)
        settings_menu.add_command(label="إعدادات النظام", command=self.open_system_settings)
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.grid(row=0, column=0, sticky="nsew")
        main_frame.grid_rowconfigure(1, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)
        
        # شريط المعلومات العلوي
        self.create_info_bar(main_frame)
        
        # منطقة المحتوى الرئيسي
        content_frame = ttk.Frame(main_frame)
        content_frame.grid(row=1, column=0, sticky="nsew", pady=(10, 0))
        content_frame.grid_rowconfigure(0, weight=1)
        content_frame.grid_columnconfigure(0, weight=1)
        content_frame.grid_columnconfigure(1, weight=1)
        
        # الإحصائيات السريعة
        self.create_stats_section(content_frame)
        
        # الأنشطة الحديثة
        self.create_recent_activities(content_frame)
        
    def create_info_bar(self, parent):
        """إنشاء شريط المعلومات العلوي"""
        info_frame = ttk.Frame(parent)
        info_frame.grid(row=0, column=0, sticky="ew", pady=(0, 10))
        info_frame.grid_columnconfigure(1, weight=1)
        
        # معلومات المستخدم
        user_info = f"مرحباً، {session_manager.get_user_name()}"
        ttk.Label(info_frame, text=user_info, font=('Arial', 12, 'bold')).grid(
            row=0, column=0, sticky="w"
        )
        
        # زر تسجيل الخروج
        logout_btn = ttk.Button(
            info_frame, 
            text="تسجيل خروج", 
            command=self.logout
        )
        logout_btn.grid(row=0, column=2, sticky="e")
        
    def create_stats_section(self, parent):
        """إنشاء قسم الإحصائيات"""
        stats_frame = ttk.LabelFrame(parent, text="الإحصائيات السريعة", padding="10")
        stats_frame.grid(row=0, column=0, sticky="nsew", padx=(0, 5))
        
        # بطاقات الإحصائيات
        self.stats_cards = {}
        
        # إجمالي المبيعات
        sales_frame = ttk.Frame(stats_frame)
        sales_frame.grid(row=0, column=0, sticky="ew", pady=5)
        ttk.Label(sales_frame, text="إجمالي المبيعات:", font=('Arial', 10, 'bold')).pack(anchor="w")
        self.stats_cards['sales'] = ttk.Label(sales_frame, text="0", font=('Arial', 14))
        self.stats_cards['sales'].pack(anchor="w")
        
        # إجمالي المنتجات
        products_frame = ttk.Frame(stats_frame)
        products_frame.grid(row=1, column=0, sticky="ew", pady=5)
        ttk.Label(products_frame, text="إجمالي المنتجات:", font=('Arial', 10, 'bold')).pack(anchor="w")
        self.stats_cards['products'] = ttk.Label(products_frame, text="0", font=('Arial', 14))
        self.stats_cards['products'].pack(anchor="w")
        
        # إجمالي العملاء
        customers_frame = ttk.Frame(stats_frame)
        customers_frame.grid(row=2, column=0, sticky="ew", pady=5)
        ttk.Label(customers_frame, text="إجمالي العملاء:", font=('Arial', 10, 'bold')).pack(anchor="w")
        self.stats_cards['customers'] = ttk.Label(customers_frame, text="0", font=('Arial', 14))
        self.stats_cards['customers'].pack(anchor="w")
        
    def create_recent_activities(self, parent):
        """إنشاء قسم الأنشطة الحديثة"""
        activities_frame = ttk.LabelFrame(parent, text="الأنشطة الحديثة", padding="10")
        activities_frame.grid(row=0, column=1, sticky="nsew", padx=(5, 0))
        activities_frame.grid_rowconfigure(0, weight=1)
        activities_frame.grid_columnconfigure(0, weight=1)
        
        # قائمة الأنشطة
        self.activities_tree = ttk.Treeview(
            activities_frame, 
            columns=('time', 'activity'), 
            show='headings',
            height=10
        )
        
        self.activities_tree.heading('time', text='الوقت')
        self.activities_tree.heading('activity', text='النشاط')
        
        self.activities_tree.column('time', width=100)
        self.activities_tree.column('activity', width=300)
        
        self.activities_tree.grid(row=0, column=0, sticky="nsew")
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(activities_frame, orient="vertical", command=self.activities_tree.yview)
        scrollbar.grid(row=0, column=1, sticky="ns")
        self.activities_tree.configure(yscrollcommand=scrollbar.set)

    def load_dashboard_data(self):
        """تحميل بيانات لوحة التحكم"""
        if not db_manager.connect():
            messagebox.showerror("خطأ", "خطأ في الاتصال بقاعدة البيانات")
            return

        try:
            # تحميل الإحصائيات
            user_id = session_manager.get_user_id()
            stats = db_manager.get_dashboard_stats(user_id)

            # تحديث بطاقات الإحصائيات
            self.stats_cards['sales'].config(text=str(stats.get('total_sales', 0)))
            self.stats_cards['products'].config(text=str(stats.get('total_products', 0)))
            self.stats_cards['customers'].config(text=str(stats.get('total_customers', 0)))

            # تحميل الأنشطة الحديثة (مثال)
            self.load_recent_activities()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل البيانات: {str(e)}")
        finally:
            db_manager.disconnect()

    def load_recent_activities(self):
        """تحميل الأنشطة الحديثة"""
        # مسح البيانات السابقة
        for item in self.activities_tree.get_children():
            self.activities_tree.delete(item)

        # إضافة أنشطة تجريبية
        sample_activities = [
            ("10:30", "تم إنشاء فاتورة جديدة"),
            ("10:15", "تم إضافة منتج جديد"),
            ("09:45", "تم تسجيل عملية بيع"),
            ("09:30", "تم تحديث المخزون"),
            ("09:00", "تم تسجيل دخول المستخدم")
        ]

        for time, activity in sample_activities:
            self.activities_tree.insert('', 'end', values=(time, activity))

    # دوال فتح الوحدات المختلفة
    def open_pos(self):
        """فتح نقاط البيع"""
        try:
            from modules.pos.pos_system import POSWindow
            pos_window = POSWindow()
            pos_window.run()
        except ImportError:
            messagebox.showinfo("قريباً", "وحدة نقاط البيع قيد التطوير")

    def open_invoices(self):
        """فتح الفواتير"""
        try:
            from modules.invoices.invoice_management import InvoiceWindow
            invoice_window = InvoiceWindow()
            invoice_window.run()
        except ImportError:
            messagebox.showinfo("قريباً", "وحدة الفواتير قيد التطوير")

    def open_customers(self):
        """فتح العملاء"""
        messagebox.showinfo("قريباً", "وحدة العملاء قيد التطوير")

    def open_products(self):
        """فتح المنتجات"""
        try:
            from modules.inventory.product_management import ProductWindow
            product_window = ProductWindow()
            product_window.run()
        except ImportError:
            messagebox.showinfo("قريباً", "وحدة المنتجات قيد التطوير")

    def open_warehouses(self):
        """فتح المخازن"""
        messagebox.showinfo("قريباً", "وحدة المخازن قيد التطوير")

    def open_inventory_reports(self):
        """فتح تقارير المخزون"""
        messagebox.showinfo("قريباً", "تقارير المخزون قيد التطوير")

    def open_employees(self):
        """فتح الموظفين"""
        try:
            from modules.users.user_management import UserWindow
            user_window = UserWindow()
            user_window.run()
        except ImportError:
            messagebox.showinfo("قريباً", "وحدة الموظفين قيد التطوير")

    def open_attendance(self):
        """فتح الحضور والانصراف"""
        messagebox.showinfo("قريباً", "وحدة الحضور والانصراف قيد التطوير")

    def open_payroll(self):
        """فتح الرواتب"""
        messagebox.showinfo("قريباً", "وحدة الرواتب قيد التطوير")

    def open_sales_reports(self):
        """فتح تقارير المبيعات"""
        try:
            from modules.reports.report_generator import ReportWindow
            report_window = ReportWindow()
            report_window.run()
        except ImportError:
            messagebox.showinfo("قريباً", "تقارير المبيعات قيد التطوير")

    def open_financial_reports(self):
        """فتح التقارير المالية"""
        messagebox.showinfo("قريباً", "التقارير المالية قيد التطوير")

    def open_company_settings(self):
        """فتح إعدادات الشركة"""
        messagebox.showinfo("قريباً", "إعدادات الشركة قيد التطوير")

    def open_users(self):
        """فتح إدارة المستخدمين"""
        try:
            from modules.users.user_management import UserWindow
            user_window = UserWindow()
            user_window.run()
        except ImportError:
            messagebox.showinfo("قريباً", "إدارة المستخدمين قيد التطوير")

    def open_system_settings(self):
        """فتح إعدادات النظام"""
        try:
            from modules.settings.settings_manager import SettingsWindow
            settings_window = SettingsWindow()
            settings_window.run()
        except ImportError:
            messagebox.showinfo("قريباً", "إعدادات النظام قيد التطوير")

    def logout(self):
        """تسجيل خروج"""
        if messagebox.askyesno("تأكيد", "هل تريد تسجيل الخروج؟"):
            session_manager.logout()
            self.window.destroy()

            # العودة إلى شاشة تسجيل الدخول
            from modules.auth.login import LoginWindow
            login_window = LoginWindow()
            login_window.run()

    def run(self):
        """تشغيل النافذة"""
        self.window.mainloop()

def main():
    """الدالة الرئيسية"""
    if not session_manager.is_logged_in():
        messagebox.showerror("خطأ", "يجب تسجيل الدخول أولاً")
        return

    app = DashboardWindow()
    app.run()

if __name__ == "__main__":
    main()
