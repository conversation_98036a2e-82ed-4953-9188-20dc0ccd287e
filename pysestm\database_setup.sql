-- إعد<PERSON> قاعدة البيانات لنظام ERP سطح المكتب
-- Database Setup for Desktop ERP System

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS erp_desktop CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE erp_desktop;

-- جدول المستخدمين
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    type ENUM('super admin', 'admin', 'company', 'employee') DEFAULT 'employee',
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_type (type),
    INDEX idx_created_by (created_by)
);

-- جدول الشركات
CREATE TABLE IF NOT EXISTS companies (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(50),
    address TEXT,
    logo VARCHAR(255),
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول فئات المنتجات
CREATE TABLE IF NOT EXISTS product_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول وحدات القياس
CREATE TABLE IF NOT EXISTS product_units (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    short_name VARCHAR(20),
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول المنتجات والخدمات
CREATE TABLE IF NOT EXISTS product_services (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    sku VARCHAR(100) UNIQUE,
    description TEXT,
    category_id INT,
    unit_id INT,
    sale_price DECIMAL(15,2) DEFAULT 0.00,
    purchase_price DECIMAL(15,2) DEFAULT 0.00,
    quantity INT DEFAULT 0,
    min_quantity INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    type ENUM('product', 'service') DEFAULT 'product',
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES product_categories(id) ON DELETE SET NULL,
    FOREIGN KEY (unit_id) REFERENCES product_units(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_sku (sku),
    INDEX idx_name (name),
    INDEX idx_category (category_id)
);

-- جدول العملاء
CREATE TABLE IF NOT EXISTS customers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(50),
    address TEXT,
    balance DECIMAL(15,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_name (name),
    INDEX idx_email (email)
);

-- جدول الموردين
CREATE TABLE IF NOT EXISTS vendors (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(50),
    address TEXT,
    balance DECIMAL(15,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول المخازن
CREATE TABLE IF NOT EXISTS warehouses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    location VARCHAR(255),
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول نقاط البيع
CREATE TABLE IF NOT EXISTS pos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    pos_id VARCHAR(100) UNIQUE NOT NULL,
    customer_id INT DEFAULT 1,
    warehouse_id INT DEFAULT 1,
    total_amount DECIMAL(15,2) NOT NULL,
    payment_method VARCHAR(50) DEFAULT 'cash',
    paid_amount DECIMAL(15,2) DEFAULT 0.00,
    change_amount DECIMAL(15,2) DEFAULT 0.00,
    status ENUM('completed', 'pending', 'cancelled') DEFAULT 'completed',
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
    FOREIGN KEY (warehouse_id) REFERENCES warehouses(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_pos_id (pos_id),
    INDEX idx_created_at (created_at)
);

-- جدول منتجات نقاط البيع
CREATE TABLE IF NOT EXISTS pos_products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    pos_id VARCHAR(100) NOT NULL,
    product_id INT NOT NULL,
    quantity INT NOT NULL,
    price DECIMAL(15,2) NOT NULL,
    total DECIMAL(15,2) NOT NULL,
    FOREIGN KEY (product_id) REFERENCES product_services(id) ON DELETE CASCADE,
    INDEX idx_pos_id (pos_id)
);

-- جدول الفواتير
CREATE TABLE IF NOT EXISTS invoices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_number VARCHAR(100) UNIQUE NOT NULL,
    customer_id INT NOT NULL,
    issue_date DATE NOT NULL,
    due_date DATE,
    subtotal DECIMAL(15,2) DEFAULT 0.00,
    tax_amount DECIMAL(15,2) DEFAULT 0.00,
    total_amount DECIMAL(15,2) NOT NULL,
    paid_amount DECIMAL(15,2) DEFAULT 0.00,
    status ENUM('draft', 'sent', 'paid', 'overdue', 'cancelled') DEFAULT 'draft',
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_invoice_number (invoice_number),
    INDEX idx_status (status)
);

-- جدول عناصر الفواتير
CREATE TABLE IF NOT EXISTS invoice_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_id INT NOT NULL,
    product_id INT NOT NULL,
    description TEXT,
    quantity INT NOT NULL,
    price DECIMAL(15,2) NOT NULL,
    total DECIMAL(15,2) NOT NULL,
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES product_services(id) ON DELETE CASCADE
);

-- إدراج بيانات تجريبية

-- إدراج مستخدم إداري افتراضي
INSERT INTO users (name, email, password, type) VALUES 
('المدير العام', '<EMAIL>', MD5('123456'), 'super admin');

-- إدراج عميل افتراضي
INSERT INTO customers (name, email, phone, created_by) VALUES 
('عميل نقدي', '<EMAIL>', '0000000000', 1);

-- إدراج مخزن افتراضي
INSERT INTO warehouses (name, location, created_by) VALUES 
('المخزن الرئيسي', 'المقر الرئيسي', 1);

-- إدراج فئات منتجات تجريبية
INSERT INTO product_categories (name, description, created_by) VALUES 
('إلكترونيات', 'الأجهزة الإلكترونية والكهربائية', 1),
('ملابس', 'الملابس والأزياء', 1),
('طعام ومشروبات', 'المواد الغذائية والمشروبات', 1);

-- إدراج وحدات قياس تجريبية
INSERT INTO product_units (name, short_name, created_by) VALUES 
('قطعة', 'قطعة', 1),
('كيلوجرام', 'كجم', 1),
('لتر', 'لتر', 1),
('متر', 'م', 1);

-- إدراج منتجات تجريبية
INSERT INTO product_services (name, sku, description, category_id, unit_id, sale_price, purchase_price, quantity, created_by) VALUES 
('لابتوب ديل', 'DELL-001', 'لابتوب ديل انسبايرون 15', 1, 1, 2500.00, 2000.00, 10, 1),
('قميص قطني', 'SHIRT-001', 'قميص قطني أزرق', 2, 1, 150.00, 100.00, 25, 1),
('عصير برتقال', 'JUICE-001', 'عصير برتقال طبيعي', 3, 3, 15.00, 10.00, 50, 1),
('ماوس لاسلكي', 'MOUSE-001', 'ماوس لاسلكي لوجيتك', 1, 1, 120.00, 80.00, 30, 1);

COMMIT;
