"""
إدارة المستخدمين
User Management
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from utils.database import db_manager
from utils.helpers import UIHelpers, DataHelpers, session_manager
from config import APP_CONFIG

class UserWindow:
    """نافذة إدارة المستخدمين"""
    
    def __init__(self):
        self.window = tk.Toplevel()
        self.setup_window()
        self.create_widgets()
        self.load_users()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("إدارة المستخدمين")
        self.window.configure(bg='#f0f0f0')
        UIHelpers.center_window(self.window, 900, 600)
        
        # إعداد الشبكة
        self.window.grid_rowconfigure(0, weight=1)
        self.window.grid_columnconfigure(0, weight=1)
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.grid(row=0, column=0, sticky="nsew")
        main_frame.grid_rowconfigure(1, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)
        
        # شريط الأدوات
        toolbar_frame = ttk.Frame(main_frame)
        toolbar_frame.grid(row=0, column=0, sticky="ew", pady=(0, 10))
        
        # أزرار الأدوات
        ttk.Button(toolbar_frame, text="إضافة مستخدم", command=self.add_user).pack(side="left", padx=(0, 5))
        ttk.Button(toolbar_frame, text="تعديل", command=self.edit_user).pack(side="left", padx=(0, 5))
        ttk.Button(toolbar_frame, text="حذف", command=self.delete_user).pack(side="left", padx=(0, 5))
        ttk.Button(toolbar_frame, text="تحديث", command=self.load_users).pack(side="left", padx=(0, 5))
        
        # حقل البحث
        search_frame = ttk.Frame(toolbar_frame)
        search_frame.pack(side="right")
        
        ttk.Label(search_frame, text="البحث:").pack(side="left", padx=(0, 5))
        self.search_entry = ttk.Entry(search_frame, width=20)
        self.search_entry.pack(side="left", padx=(0, 5))
        self.search_entry.bind('<KeyRelease>', self.search_users)
        
        # جدول المستخدمين
        self.create_users_table(main_frame)
        
    def create_users_table(self, parent):
        """إنشاء جدول المستخدمين"""
        # إطار الجدول
        table_frame = ttk.Frame(parent)
        table_frame.grid(row=1, column=0, sticky="nsew")
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # الجدول
        columns = ('id', 'name', 'email', 'type', 'is_active', 'created_at')
        self.users_tree = ttk.Treeview(table_frame, columns=columns, show='headings')
        
        # تعيين العناوين
        headings = ['المعرف', 'الاسم', 'البريد الإلكتروني', 'النوع', 'نشط', 'تاريخ الإنشاء']
        for col, heading in zip(columns, headings):
            self.users_tree.heading(col, text=heading)
            self.users_tree.column(col, width=120)
        
        # إخفاء عمود المعرف
        self.users_tree.column('id', width=0, stretch=False)
        self.users_tree.heading('id', text='')
        
        self.users_tree.grid(row=0, column=0, sticky="nsew")
        
        # شريط التمرير
        scrollbar_y = ttk.Scrollbar(table_frame, orient="vertical", command=self.users_tree.yview)
        scrollbar_y.grid(row=0, column=1, sticky="ns")
        self.users_tree.configure(yscrollcommand=scrollbar_y.set)
        
        scrollbar_x = ttk.Scrollbar(table_frame, orient="horizontal", command=self.users_tree.xview)
        scrollbar_x.grid(row=1, column=0, sticky="ew")
        self.users_tree.configure(xscrollcommand=scrollbar_x.set)
        
        # ربط النقر المزدوج
        self.users_tree.bind('<Double-1>', lambda e: self.edit_user())
        
    def load_users(self):
        """تحميل المستخدمين"""
        # مسح البيانات السابقة
        for item in self.users_tree.get_children():
            self.users_tree.delete(item)
        
        if not db_manager.connect():
            messagebox.showerror("خطأ", "خطأ في الاتصال بقاعدة البيانات")
            return
        
        try:
            # استعلام المستخدمين
            query = """
            SELECT id, name, email, type, is_active, created_at 
            FROM users 
            WHERE created_by = %s OR type = 'super admin'
            ORDER BY created_at DESC
            """
            users = db_manager.execute_query(query, (session_manager.get_user_id(),))
            
            if users:
                for user in users:
                    # تنسيق البيانات
                    is_active = "نعم" if user['is_active'] else "لا"
                    created_at = DataHelpers.format_date(user['created_at'])
                    
                    self.users_tree.insert('', 'end', values=(
                        user['id'],
                        user['name'],
                        user['email'],
                        user['type'],
                        is_active,
                        created_at
                    ))
                    
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل المستخدمين: {str(e)}")
        finally:
            db_manager.disconnect()
    
    def search_users(self, event=None):
        """البحث في المستخدمين"""
        search_term = self.search_entry.get().lower()
        
        # إخفاء جميع العناصر
        for item in self.users_tree.get_children():
            self.users_tree.delete(item)
        
        if not db_manager.connect():
            return
        
        try:
            # استعلام البحث
            query = """
            SELECT id, name, email, type, is_active, created_at 
            FROM users 
            WHERE (created_by = %s OR type = 'super admin')
            AND (LOWER(name) LIKE %s OR LOWER(email) LIKE %s)
            ORDER BY created_at DESC
            """
            search_pattern = f"%{search_term}%"
            users = db_manager.execute_query(query, (session_manager.get_user_id(), search_pattern, search_pattern))
            
            if users:
                for user in users:
                    is_active = "نعم" if user['is_active'] else "لا"
                    created_at = DataHelpers.format_date(user['created_at'])
                    
                    self.users_tree.insert('', 'end', values=(
                        user['id'],
                        user['name'],
                        user['email'],
                        user['type'],
                        is_active,
                        created_at
                    ))
                    
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في البحث: {str(e)}")
        finally:
            db_manager.disconnect()
    
    def add_user(self):
        """إضافة مستخدم جديد"""
        self.open_user_form()
    
    def edit_user(self):
        """تعديل مستخدم"""
        selected = self.users_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم للتعديل")
            return
        
        user_id = self.users_tree.item(selected[0])['values'][0]
        self.open_user_form(user_id)
    
    def delete_user(self):
        """حذف مستخدم"""
        selected = self.users_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم للحذف")
            return
        
        user_id = self.users_tree.item(selected[0])['values'][0]
        user_name = self.users_tree.item(selected[0])['values'][1]
        
        if messagebox.askyesno("تأكيد الحذف", f"هل تريد حذف المستخدم '{user_name}'؟"):
            if not db_manager.connect():
                messagebox.showerror("خطأ", "خطأ في الاتصال بقاعدة البيانات")
                return
            
            try:
                query = "DELETE FROM users WHERE id = %s"
                result = db_manager.execute_query(query, (user_id,))
                
                if result:
                    messagebox.showinfo("نجح", "تم حذف المستخدم بنجاح")
                    self.load_users()
                else:
                    messagebox.showerror("خطأ", "فشل في حذف المستخدم")
                    
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في حذف المستخدم: {str(e)}")
            finally:
                db_manager.disconnect()
    
    def open_user_form(self, user_id=None):
        """فتح نموذج المستخدم"""
        UserFormWindow(self, user_id)
    
    def run(self):
        """تشغيل النافذة"""
        self.window.mainloop()

class UserFormWindow:
    """نافذة نموذج المستخدم"""
    
    def __init__(self, parent, user_id=None):
        self.parent = parent
        self.user_id = user_id
        self.window = tk.Toplevel(parent.window)
        self.setup_window()
        self.create_widgets()
        
        if user_id:
            self.load_user_data()
    
    def setup_window(self):
        """إعداد النافذة"""
        title = "تعديل مستخدم" if self.user_id else "إضافة مستخدم جديد"
        self.window.title(title)
        self.window.configure(bg='#f0f0f0')
        UIHelpers.center_window(self.window, 400, 350)
        self.window.resizable(False, False)
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent.window)
        self.window.grab_set()
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.grid(row=0, column=0, sticky="nsew")
        
        # الحقول
        row = 0
        
        # الاسم
        ttk.Label(main_frame, text="الاسم:").grid(row=row, column=0, sticky="w", pady=5)
        self.name_entry = ttk.Entry(main_frame, width=25)
        self.name_entry.grid(row=row, column=1, pady=5, padx=(10, 0))
        row += 1
        
        # البريد الإلكتروني
        ttk.Label(main_frame, text="البريد الإلكتروني:").grid(row=row, column=0, sticky="w", pady=5)
        self.email_entry = ttk.Entry(main_frame, width=25)
        self.email_entry.grid(row=row, column=1, pady=5, padx=(10, 0))
        row += 1
        
        # كلمة المرور
        ttk.Label(main_frame, text="كلمة المرور:").grid(row=row, column=0, sticky="w", pady=5)
        self.password_entry = ttk.Entry(main_frame, width=25, show="*")
        self.password_entry.grid(row=row, column=1, pady=5, padx=(10, 0))
        row += 1
        
        # نوع المستخدم
        ttk.Label(main_frame, text="نوع المستخدم:").grid(row=row, column=0, sticky="w", pady=5)
        self.type_combo = ttk.Combobox(main_frame, width=22, values=['company', 'employee', 'admin'])
        self.type_combo.grid(row=row, column=1, pady=5, padx=(10, 0))
        self.type_combo.set('employee')
        row += 1
        
        # حالة النشاط
        self.is_active_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(main_frame, text="نشط", variable=self.is_active_var).grid(
            row=row, column=0, columnspan=2, sticky="w", pady=5
        )
        row += 1
        
        # الأزرار
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=row, column=0, columnspan=2, pady=20)
        
        ttk.Button(buttons_frame, text="حفظ", command=self.save_user).pack(side="left", padx=(0, 10))
        ttk.Button(buttons_frame, text="إلغاء", command=self.window.destroy).pack(side="left")
    
    def load_user_data(self):
        """تحميل بيانات المستخدم للتعديل"""
        if not db_manager.connect():
            messagebox.showerror("خطأ", "خطأ في الاتصال بقاعدة البيانات")
            return
        
        try:
            query = "SELECT name, email, type, is_active FROM users WHERE id = %s"
            result = db_manager.execute_query(query, (self.user_id,))
            
            if result and len(result) > 0:
                user = result[0]
                self.name_entry.insert(0, user['name'])
                self.email_entry.insert(0, user['email'])
                self.type_combo.set(user['type'])
                self.is_active_var.set(bool(user['is_active']))
                
                # إخفاء حقل كلمة المرور في التعديل
                self.password_entry.config(state='disabled')
                
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل بيانات المستخدم: {str(e)}")
        finally:
            db_manager.disconnect()
    
    def save_user(self):
        """حفظ المستخدم"""
        # التحقق من البيانات
        name = self.name_entry.get().strip()
        email = self.email_entry.get().strip()
        password = self.password_entry.get().strip()
        user_type = self.type_combo.get()
        is_active = self.is_active_var.get()
        
        # التحقق من الحقول المطلوبة
        required_fields = {'الاسم': name, 'البريد الإلكتروني': email}
        if not self.user_id:  # إضافة جديدة
            required_fields['كلمة المرور'] = password
        
        empty_fields = DataHelpers.validate_required_fields(required_fields)
        if empty_fields:
            messagebox.showerror("خطأ", f"الحقول التالية مطلوبة: {', '.join(empty_fields)}")
            return
        
        # التحقق من صحة البريد الإلكتروني
        if not DataHelpers.validate_email(email):
            messagebox.showerror("خطأ", "البريد الإلكتروني غير صحيح")
            return
        
        if not db_manager.connect():
            messagebox.showerror("خطأ", "خطأ في الاتصال بقاعدة البيانات")
            return
        
        try:
            if self.user_id:  # تعديل
                query = """
                UPDATE users 
                SET name = %s, email = %s, type = %s, is_active = %s 
                WHERE id = %s
                """
                params = (name, email, user_type, is_active, self.user_id)
            else:  # إضافة جديدة
                hashed_password = DataHelpers.hash_password(password)
                query = """
                INSERT INTO users (name, email, password, type, is_active, created_by) 
                VALUES (%s, %s, %s, %s, %s, %s)
                """
                params = (name, email, hashed_password, user_type, is_active, session_manager.get_user_id())
            
            result = db_manager.execute_query(query, params)
            
            if result:
                action = "تم تحديث" if self.user_id else "تم إضافة"
                messagebox.showinfo("نجح", f"{action} المستخدم بنجاح")
                self.parent.load_users()
                self.window.destroy()
            else:
                messagebox.showerror("خطأ", "فشل في حفظ المستخدم")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ المستخدم: {str(e)}")
        finally:
            db_manager.disconnect()

def main():
    """الدالة الرئيسية"""
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    app = UserWindow()
    app.run()

if __name__ == "__main__":
    main()
