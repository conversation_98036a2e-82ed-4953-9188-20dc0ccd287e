@echo off
echo ========================================
echo    نظام ERP سطح المكتب
echo    Desktop ERP System
echo ========================================
echo.

echo تحقق من Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python 3.8 أو أحدث
    pause
    exit /b 1
)

echo تحقق من المكتبات المطلوبة...
pip show mysql-connector-python >nul 2>&1
if errorlevel 1 (
    echo تثبيت المكتبات المطلوبة...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo خطأ في تثبيت المكتبات
        pause
        exit /b 1
    )
)

echo بدء تشغيل البرنامج...
python main.py

if errorlevel 1 (
    echo.
    echo حدث خطأ في تشغيل البرنامج
    echo يرجى التحقق من:
    echo 1. إعدادات قاعدة البيانات في config.py
    echo 2. تشغيل MySQL Server
    echo 3. إنشاء قاعدة البيانات باستخدام database_setup.sql
    echo.
)

pause
